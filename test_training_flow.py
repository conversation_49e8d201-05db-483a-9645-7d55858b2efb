#!/usr/bin/env python3
"""
Test script to verify the complete training flow from Web GUI to Training Service
"""

import asyncio
import json
import websockets
import logging
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """Test WebSocket connection to the bridge"""
    try:
        uri = "ws://localhost:8080"
        logger.info(f"Connecting to {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ Connected to WebSocket bridge")
            
            # Listen for initial messages
            logger.info("Waiting for initial messages...")
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    logger.info(f"Received: {data['type']} - {data.get('data', {}).get('state', 'N/A')}")
                except asyncio.TimeoutError:
                    logger.info("No more initial messages")
                    break
            
            # Test training command
            logger.info("\n🚀 Testing training start command...")
            training_command = {
                "type": "training_command",
                "command": "start",
                "parameters": {
                    "config": {
                        "task_name": "anymal_c_flat",
                        "num_envs": 512,
                        "max_iterations": 100,
                        "learning_rate": 0.0003,
                        "experiment_name": "test_experiment",
                        "run_name": "test_run_" + str(int(time.time()))
                    }
                }
            }
            
            await websocket.send(json.dumps(training_command))
            logger.info("Training start command sent")
            
            # Listen for responses
            logger.info("Listening for training responses...")
            for i in range(10):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(message)
                    if data['type'] == 'training_status':
                        status = data['data']
                        logger.info(f"Training Status: {status.get('state')} - Iteration: {status.get('current_iteration', 0)}")
                    elif data['type'] == 'training_metrics':
                        metrics = data['data']
                        logger.info(f"Training Metrics: Reward: {metrics.get('mean_reward', 0):.2f}")
                except asyncio.TimeoutError:
                    logger.info("No more messages received")
                    break
            
            # Test stop command
            logger.info("\n🛑 Testing training stop command...")
            stop_command = {
                "type": "training_command",
                "command": "stop"
            }
            
            await websocket.send(json.dumps(stop_command))
            logger.info("Training stop command sent")
            
            # Listen for stop response
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                data = json.loads(message)
                if data['type'] == 'training_status':
                    status = data['data']
                    logger.info(f"Final Training Status: {status.get('state')}")
            except asyncio.TimeoutError:
                logger.info("No stop response received")
            
            logger.info("✅ Test completed successfully")
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function"""
    logger.info("🧪 Starting training flow test...")
    logger.info("Make sure the following services are running:")
    logger.info("  1. Training Service (start_training_service.py)")
    logger.info("  2. Web Backend Bridge (start_web_backend.py)")
    logger.info("  3. Web GUI (npm run dev)")
    
    await asyncio.sleep(2)  # Give user time to read
    
    await test_websocket_connection()

if __name__ == "__main__":
    asyncio.run(main())
