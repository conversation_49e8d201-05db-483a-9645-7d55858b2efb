"""
Enhanced Zenoh session manager with advanced features for the legged robot training system.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Optional, Callable, Any, List, Set
from dataclasses import dataclass, field
from enum import Enum
import zenoh
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

from .message_format import StandardZenohMessage, MessageFactory, MessageType, Priority
from .data_models import SerializationMixin
from . import topics

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """Connection state enumeration"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"

class QoSLevel(Enum):
    """Quality of Service levels"""
    BEST_EFFORT = "best_effort"
    RELIABLE = "reliable"
    PERSISTENT = "persistent"

@dataclass
class EnhancedZenohConfig:
    """Enhanced configuration for Zenoh session"""
    # Connection settings
    router_endpoints: List[str] = field(default_factory=lambda: ["tcp/127.0.0.1:7447"])
    session_timeout: float = 30.0
    connect_timeout: float = 10.0
    
    # QoS settings
    default_qos_reliability: QoSLevel = QoSLevel.RELIABLE
    default_qos_durability: QoSLevel = QoSLevel.PERSISTENT
    
    # Performance settings
    max_publishers: int = 100
    max_subscribers: int = 100
    message_buffer_size: int = 1000
    thread_pool_size: int = 4
    
    # Reconnection settings
    enable_auto_reconnect: bool = True
    reconnect_interval: float = 5.0
    max_reconnect_attempts: int = 10
    
    # Monitoring settings
    enable_heartbeat: bool = True
    heartbeat_interval: float = 30.0
    enable_metrics: bool = True
    
    # Security settings (placeholder for future enhancement)
    enable_encryption: bool = False
    cert_path: Optional[str] = None
    key_path: Optional[str] = None

@dataclass
class TopicConfig:
    """Configuration for individual topics"""
    topic_name: str
    qos_reliability: QoSLevel = QoSLevel.RELIABLE
    qos_durability: QoSLevel = QoSLevel.PERSISTENT
    message_retention_count: int = 100
    enable_metrics: bool = True

@dataclass
class SessionMetrics:
    """Metrics for Zenoh session"""
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    connection_uptime: float = 0.0
    last_heartbeat: float = 0.0
    active_publishers: int = 0
    active_subscribers: int = 0
    reconnection_count: int = 0
    error_count: int = 0

class EnhancedZenohSessionManager:
    """
    Enhanced Zenoh session manager with advanced features:
    - Automatic reconnection
    - QoS management
    - Topic lifecycle management
    - Performance monitoring
    - Health checking
    - Thread pool for async operations
    """
    
    def __init__(self, config: EnhancedZenohConfig = None, service_name: str = "legged_gym_service"):
        self.config = config or EnhancedZenohConfig()
        self.service_name = service_name
        
        # Core Zenoh components
        self.session = None
        self.publishers: Dict[str, Any] = {}
        self.subscribers: Dict[str, Any] = {}
        self.topic_configs: Dict[str, TopicConfig] = {}
        
        # State management
        self.connection_state = ConnectionState.DISCONNECTED
        self.is_initialized = False
        self.session_id = str(uuid.uuid4())
        self.start_time = time.time()
        
        # Monitoring and metrics
        self.metrics = SessionMetrics()
        self.callbacks: Dict[str, List[Callable]] = {}
        self.message_buffer: Dict[str, List[Any]] = {}
        
        # Threading
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config.thread_pool_size)
        self.reconnect_task = None
        self.heartbeat_task = None
        self.monitoring_task = None
        
        # Synchronization
        self.lock = threading.RLock()
        self.shutdown_event = asyncio.Event() if asyncio._get_running_loop() else threading.Event()
    
    async def initialize(self) -> bool:
        """Initialize the Zenoh session with enhanced configuration"""
        if self.is_initialized:
            logger.warning("Session already initialized")
            return True
        
        try:
            self.connection_state = ConnectionState.CONNECTING
            logger.info(f"Initializing Zenoh session {self.session_id} for service {self.service_name}")
            
            # Configure Zenoh session
            zenoh_config = await self._create_zenoh_config()
            
            # Open session with timeout
            self.session = await asyncio.wait_for(
                self._open_session(zenoh_config),
                timeout=self.config.connect_timeout
            )
            
            # Set connection state
            self.connection_state = ConnectionState.CONNECTED
            self.is_initialized = True
            self.start_time = time.time()
            
            # Start background tasks
            await self._start_background_tasks()
            
            logger.info(f"Zenoh session initialized successfully. Session ID: {self.session_id}")
            return True
            
        except Exception as e:
            self.connection_state = ConnectionState.ERROR
            self.metrics.error_count += 1
            logger.error(f"Failed to initialize Zenoh session: {e}")
            return False
    
    async def _create_zenoh_config(self) -> zenoh.Config:
        """Create Zenoh configuration"""
        config = zenoh.Config()
        
        try:
            # Set connection endpoints
            if self.config.router_endpoints:
                endpoints_str = ','.join(f'"{ep}"' for ep in self.config.router_endpoints)
                config.insert_json("connect/endpoints", f'[{endpoints_str}]')
            
            # Set session mode
            config.insert_json("mode", '"client"')
            
            # Set timeout
            config.insert_json("connect/timeout_ms", str(int(self.config.connect_timeout * 1000)))
            
            logger.debug(f"Created Zenoh config with endpoints: {self.config.router_endpoints}")
            
        except Exception as e:
            logger.warning(f"Error configuring Zenoh session, using defaults: {e}")
            config = zenoh.Config()
        
        return config
    
    async def _open_session(self, config: zenoh.Config):
        """Open Zenoh session"""
        return zenoh.open(config)
    
    async def _start_background_tasks(self):
        """Start background monitoring tasks"""
        try:
            # Start heartbeat if enabled
            if self.config.enable_heartbeat:
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            # Start monitoring if enabled
            if self.config.enable_metrics:
                self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.debug("Background tasks started")
            
        except Exception as e:
            logger.error(f"Error starting background tasks: {e}")
    
    async def register_topic(self, topic_name: str, topic_config: TopicConfig = None):
        """Register a topic with specific configuration"""
        with self.lock:
            if topic_config is None:
                topic_config = TopicConfig(
                    topic_name=topic_name,
                    qos_reliability=self.config.default_qos_reliability,
                    qos_durability=self.config.default_qos_durability
                )
            
            self.topic_configs[topic_name] = topic_config
            
            # Initialize message buffer if needed
            if topic_config.message_retention_count > 0:
                self.message_buffer[topic_name] = []
            
            logger.debug(f"Registered topic: {topic_name}")
    
    async def create_publisher(self, topic: str, qos_config: TopicConfig = None) -> Any:
        """Create an enhanced publisher with QoS configuration"""
        if not self.is_initialized:
            raise RuntimeError("Session not initialized. Call initialize() first.")
        
        with self.lock:
            if topic in self.publishers:
                logger.debug(f"Publisher for topic {topic} already exists")
                return self.publishers[topic]
            
            if len(self.publishers) >= self.config.max_publishers:
                raise RuntimeError(f"Maximum number of publishers ({self.config.max_publishers}) reached")
            
            try:
                # Register topic if not already registered
                if topic not in self.topic_configs:
                    await self.register_topic(topic, qos_config)
                
                # Create publisher
                publisher = self.session.declare_publisher(topic)
                self.publishers[topic] = publisher
                self.metrics.active_publishers += 1
                
                logger.info(f"Created publisher for topic: {topic}")
                return publisher
                
            except Exception as e:
                self.metrics.error_count += 1
                logger.error(f"Failed to create publisher for topic {topic}: {e}")
                raise
    
    async def create_subscriber(self, topic: str, callback: Callable, qos_config: TopicConfig = None) -> Any:
        """Create an enhanced subscriber with callback management"""
        if not self.is_initialized:
            raise RuntimeError("Session not initialized. Call initialize() first.")
        
        with self.lock:
            if topic in self.subscribers:
                # Add callback to existing subscriber
                if topic not in self.callbacks:
                    self.callbacks[topic] = []
                self.callbacks[topic].append(callback)
                logger.debug(f"Added callback to existing subscriber for topic: {topic}")
                return self.subscribers[topic]
            
            if len(self.subscribers) >= self.config.max_subscribers:
                raise RuntimeError(f"Maximum number of subscribers ({self.config.max_subscribers}) reached")
            
            try:
                # Register topic if not already registered
                if topic not in self.topic_configs:
                    await self.register_topic(topic, qos_config)
                
                # Initialize callback list
                self.callbacks[topic] = [callback]
                
                # Create enhanced callback wrapper
                def enhanced_callback(sample):
                    self._handle_message(topic, sample)
                
                # Create subscriber
                subscriber = self.session.declare_subscriber(topic, enhanced_callback)
                self.subscribers[topic] = subscriber
                self.metrics.active_subscribers += 1
                
                logger.info(f"Created subscriber for topic: {topic}")
                return subscriber
                
            except Exception as e:
                self.metrics.error_count += 1
                logger.error(f"Failed to create subscriber for topic {topic}: {e}")
                raise
    
    def _handle_message(self, topic: str, sample):
        """Enhanced message handler with metrics and buffering"""
        try:
            # Update metrics
            self.metrics.messages_received += 1
            self.metrics.bytes_received += len(sample.payload)
            
            # Try to deserialize as StandardZenohMessage first
            try:
                message = StandardZenohMessage.from_msgpack(sample.payload)
            except:
                # Fallback to basic message format
                import msgpack
                payload_data = msgpack.unpackb(sample.payload, raw=False)
                message = payload_data
            
            # Buffer message if configured
            if topic in self.message_buffer:
                buffer = self.message_buffer[topic]
                buffer.append(message)
                
                # Maintain buffer size
                config = self.topic_configs.get(topic)
                if config and len(buffer) > config.message_retention_count:
                    buffer.pop(0)  # Remove oldest message
            
            # Call registered callbacks
            if topic in self.callbacks:
                for callback in self.callbacks[topic]:
                    try:
                        # Execute callback in thread pool to avoid blocking
                        self.thread_pool.submit(callback, message)
                    except Exception as e:
                        logger.error(f"Error in callback for topic {topic}: {e}")
                        self.metrics.error_count += 1
                        
        except Exception as e:
            logger.error(f"Error handling message from topic {topic}: {e}")
            self.metrics.error_count += 1
    
    async def publish_message(self, topic: str, message: StandardZenohMessage):
        """Publish a StandardZenohMessage"""
        if not self.is_initialized:
            raise RuntimeError("Session not initialized. Call initialize() first.")
        
        try:
            # Create publisher if it doesn't exist
            if topic not in self.publishers:
                await self.create_publisher(topic)
            
            # Serialize message
            payload = message.to_msgpack()
            
            # Publish
            self.publishers[topic].put(payload)
            
            # Update metrics
            self.metrics.messages_sent += 1
            self.metrics.bytes_sent += len(payload)
            
            logger.debug(f"Published message to topic: {topic}")
            
        except Exception as e:
            self.metrics.error_count += 1
            logger.error(f"Failed to publish message to topic {topic}: {e}")
            raise
    
    async def publish_data(self, topic: str, data: Any, message_type: MessageType = MessageType.DATA, priority: Priority = Priority.NORMAL):
        """Publish data with automatic message creation"""
        # Create StandardZenohMessage
        message = MessageFactory.create_data(
            data=data if isinstance(data, dict) else {"data": data},
            source=self.service_name
        )
        message.header.message_type = message_type
        message.header.priority = priority
        
        await self.publish_message(topic, message)
    
    async def get_message_history(self, topic: str, count: int = None) -> List[Any]:
        """Get message history for a topic"""
        with self.lock:
            if topic not in self.message_buffer:
                return []
            
            buffer = self.message_buffer[topic]
            if count is None:
                return buffer.copy()
            else:
                return buffer[-count:] if count > 0 else buffer[:abs(count)]
    
    async def get_metrics(self) -> SessionMetrics:
        """Get current session metrics"""
        # Update uptime
        if self.connection_state == ConnectionState.CONNECTED:
            self.metrics.connection_uptime = time.time() - self.start_time
        
        return self.metrics
    
    async def get_session_info(self) -> Dict[str, Any]:
        """Get comprehensive session information"""
        metrics = await self.get_metrics()
        return {
            "session_id": self.session_id,
            "service_name": self.service_name,
            "connection_state": self.connection_state.value,
            "is_initialized": self.is_initialized,
            "start_time": self.start_time,
            "active_topics": {
                "publishers": list(self.publishers.keys()),
                "subscribers": list(self.subscribers.keys())
            },
            "metrics": {
                "messages_sent": metrics.messages_sent,
                "messages_received": metrics.messages_received,
                "bytes_sent": metrics.bytes_sent,
                "bytes_received": metrics.bytes_received,
                "connection_uptime": metrics.connection_uptime,
                "active_publishers": metrics.active_publishers,
                "active_subscribers": metrics.active_subscribers,
                "error_count": metrics.error_count
            },
            "config": {
                "router_endpoints": self.config.router_endpoints,
                "auto_reconnect": self.config.enable_auto_reconnect,
                "heartbeat_enabled": self.config.enable_heartbeat
            }
        }
    
    async def _heartbeat_loop(self):
        """Background heartbeat task"""
        while not self.shutdown_event.is_set() if hasattr(self.shutdown_event, 'is_set') else not self.shutdown_event.wait(0):
            try:
                if self.is_initialized and self.connection_state == ConnectionState.CONNECTED:
                    # Send heartbeat
                    heartbeat_msg = MessageFactory.create_heartbeat(
                        self.service_name,
                        {"session_id": self.session_id, "uptime": time.time() - self.start_time}
                    )
                    
                    await self.publish_message(topics.SYSTEM_HEALTH, heartbeat_msg)
                    self.metrics.last_heartbeat = time.time()
                
                await asyncio.sleep(self.config.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                await asyncio.sleep(self.config.heartbeat_interval)
    
    async def _monitoring_loop(self):
        """Background monitoring task"""
        while not self.shutdown_event.is_set() if hasattr(self.shutdown_event, 'is_set') else not self.shutdown_event.wait(0):
            try:
                if self.is_initialized:
                    # Publish system status
                    status_data = await self.get_session_info()
                    status_msg = MessageFactory.create_status(status_data, self.service_name)
                    
                    await self.publish_message(topics.SYSTEM_STATUS, status_msg)
                
                await asyncio.sleep(60)  # Every minute
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info(f"Shutting down Zenoh session {self.session_id}")
        
        try:
            # Signal shutdown
            if hasattr(self.shutdown_event, 'set'):
                self.shutdown_event.set()
            else:
                self.shutdown_event.set()
            
            # Cancel background tasks
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.reconnect_task:
                self.reconnect_task.cancel()
            
            # Wait for tasks to complete
            await asyncio.sleep(0.1)
            
            # Cleanup Zenoh resources
            await self.cleanup()
            
            # Shutdown thread pool
            self.thread_pool.shutdown(wait=True)
            
            logger.info("Session shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def cleanup(self):
        """Enhanced cleanup with proper resource management"""
        try:
            with self.lock:
                # Close publishers
                for topic, publisher in list(self.publishers.items()):
                    try:
                        publisher.undeclare()
                        logger.debug(f"Closed publisher for topic: {topic}")
                    except Exception as e:
                        logger.warning(f"Error closing publisher for topic {topic}: {e}")
                
                # Close subscribers
                for topic, subscriber in list(self.subscribers.items()):
                    try:
                        subscriber.undeclare()
                        logger.debug(f"Closed subscriber for topic: {topic}")
                    except Exception as e:
                        logger.warning(f"Error closing subscriber for topic {topic}: {e}")
                
                # Close session
                if self.session:
                    try:
                        self.session.close()
                        logger.info("Zenoh session closed")
                    except Exception as e:
                        logger.warning(f"Error closing Zenoh session: {e}")
                
                # Reset state
                self.connection_state = ConnectionState.DISCONNECTED
                self.is_initialized = False
                self.publishers.clear()
                self.subscribers.clear()
                self.callbacks.clear()
                self.message_buffer.clear()
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """Destructor with proper cleanup"""
        if self.is_initialized:
            try:
                # Try to create cleanup task if event loop exists
                try:
                    loop = asyncio.get_running_loop()
                    asyncio.create_task(self.shutdown())
                except RuntimeError:
                    # No event loop running, do synchronous cleanup
                    pass
            except Exception:
                pass