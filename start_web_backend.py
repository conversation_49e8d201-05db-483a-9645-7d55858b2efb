#!/usr/bin/env python3
"""
Simple web backend for EngineAI Legged Gym
Provides WebSocket interface for the React frontend
"""

import asyncio
import json
import logging
import websockets
import signal
import sys
from datetime import datetime
from typing import Dict, Set

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebBackend:
    """Simple web backend for demo purposes"""
    
    def __init__(self, host="localhost", port=8080):
        self.host = host
        self.port = port
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.running = False
        
        # Mock data
        self.system_status = {
            "is_healthy": True,
            "active_services": ["zenoh_router", "training_service"],
            "system_load": {"cpu": 45.2, "memory": 62.1, "gpu": 78.5},
            "error_count": 0,
            "last_update": datetime.now().timestamp(),
            "uptime": 3600
        }
        
        self.training_status = {
            "status": "idle",
            "current_iteration": 0,
            "max_iterations": 1500,
            "current_reward": 0.0,
            "best_reward": 0.0,
            "learning_rate": 0.0003,
            "experiment_name": "demo_experiment",
            "run_name": "demo_run"
        }
    
    async def register_client(self, websocket):
        """Register a new client"""
        self.clients.add(websocket)
        logger.info(f"Client connected: {websocket.remote_address}")
        
        # Send initial data
        await self.send_to_client(websocket, {
            "type": "system_status",
            "data": self.system_status
        })
        
        await self.send_to_client(websocket, {
            "type": "training_status", 
            "data": self.training_status
        })
    
    async def unregister_client(self, websocket):
        """Unregister a client"""
        self.clients.discard(websocket)
        logger.info(f"Client disconnected: {websocket.remote_address}")
    
    async def send_to_client(self, websocket, message):
        """Send message to a specific client"""
        try:
            await websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            pass
    
    async def broadcast(self, message):
        """Broadcast message to all clients"""
        if self.clients:
            await asyncio.gather(
                *[self.send_to_client(client, message) for client in self.clients],
                return_exceptions=True
            )
    
    async def handle_client(self, websocket, path):
        """Handle client connection"""
        logger.info(f"New WebSocket connection from {websocket.remote_address} on path: {path}")
        await self.register_client(websocket)
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(websocket, data)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON from client: {message}")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"WebSocket connection closed: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            await self.unregister_client(websocket)
    
    async def handle_message(self, websocket, data):
        """Handle incoming message from client"""
        message_type = data.get("type")
        
        if message_type == "training_command":
            command = data.get("command")
            logger.info(f"Received training command: {command}")
            
            if command == "start":
                self.training_status["status"] = "running"
                await self.broadcast({
                    "type": "training_status",
                    "data": self.training_status
                })
            elif command == "stop":
                self.training_status["status"] = "idle"
                await self.broadcast({
                    "type": "training_status", 
                    "data": self.training_status
                })
        
        elif message_type == "get_status":
            await self.send_to_client(websocket, {
                "type": "system_status",
                "data": self.system_status
            })
    
    async def update_loop(self):
        """Periodic update loop"""
        while self.running:
            # Update mock data
            self.system_status["last_update"] = datetime.now().timestamp()
            self.system_status["uptime"] += 5
            
            if self.training_status["status"] == "running":
                self.training_status["current_iteration"] += 1
                self.training_status["current_reward"] += 0.1
                if self.training_status["current_reward"] > self.training_status["best_reward"]:
                    self.training_status["best_reward"] = self.training_status["current_reward"]
            
            # Broadcast updates
            await self.broadcast({
                "type": "system_status",
                "data": self.system_status
            })
            
            if self.training_status["status"] == "running":
                await self.broadcast({
                    "type": "training_status",
                    "data": self.training_status
                })
            
            await asyncio.sleep(5)  # Update every 5 seconds
    
    async def start(self):
        """Start the web backend"""
        self.running = True
        
        logger.info(f"🚀 Starting web backend on ws://{self.host}:{self.port}")
        
        # Start WebSocket server
        server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port
        )
        
        # Start update loop
        update_task = asyncio.create_task(self.update_loop())
        
        logger.info("✅ Web backend started successfully")
        logger.info(f"   - WebSocket server: ws://{self.host}:{self.port}")
        logger.info(f"   - Ready for frontend connections")
        
        # Wait for server to close
        await server.wait_closed()
        update_task.cancel()
    
    def stop(self):
        """Stop the web backend"""
        self.running = False
        logger.info("Web backend stopped")

def signal_handler(backend):
    """Handle shutdown signals"""
    def handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        backend.stop()
    return handler

async def main():
    """Main entry point"""
    backend = WebBackend()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler(backend))
    signal.signal(signal.SIGTERM, signal_handler(backend))
    
    try:
        await backend.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Backend error: {e}")
        return 1
    finally:
        backend.stop()
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
