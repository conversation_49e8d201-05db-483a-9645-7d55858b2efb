#!/usr/bin/env python3
"""
Simple training service starter for EngineAI Legged Gym
"""

import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Start training service"""
    try:
        logger.info("🚀 Starting EngineAI Training Service...")
        
        # Import here to avoid early Isaac Gym loading
        from zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
        
        # Create Zenoh session manager
        zenoh_config = EnhancedZenohConfig(
            router_endpoints=["tcp/127.0.0.1:7447"],
            enable_heartbeat=True,
            enable_metrics=True
        )
        
        session_manager = EnhancedZenohSessionManager(zenoh_config, "training_service")
        
        # Initialize session
        logger.info("Initializing Zenoh session...")
        await session_manager.initialize()
        
        logger.info("✅ Training service started successfully")
        logger.info("   - Zenoh session connected")
        logger.info("   - Ready to receive training commands")
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Training service interrupted")
    except Exception as e:
        logger.error(f"Training service error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        if 'session_manager' in locals():
            await session_manager.shutdown()
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
